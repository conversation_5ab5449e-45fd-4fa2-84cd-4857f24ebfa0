package id.co.panindaiichilife.superapp.agent.service.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.BirthdayCustomerDto;
import id.co.panindaiichilife.superapp.agent.api.filter.BirthdayCustomerFilter;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalBirthdayCustomerResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.util.AgentCodeUtil;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class BirthdayCustomerService {

    private final UserRepository userRepository;

    private final AgentRepository agentRepository;

    private final PortalProvider portalProvider;

    /**
     * Get birthday customer list for an agent
     *
     * @param username The username of the agent
     * @param filter   The filter parameters
     * @return List of birthday customer DTOs
     */
    @CacheableWithTTL(cacheName = "birthdayCustomerListCache",
            key = "(T(java.util.Objects).nonNull(#filter.agentCode) ? #filter.agentCode : #username) + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.startDate) ? #filter.startDate : 'default') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.customerName) ? #filter.customerName : '-') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.endDate) ? #filter.endDate : 'default')",
            ttl = 1600, db = 7)
    public List<BirthdayCustomerDto> getBirthdayCustomerList(String username, BirthdayCustomerFilter filter) {
        // Get the agent information from the username
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        // If agentCode is not provided in the filter, use the agent's code
        String agentCode = StringUtils.isBlank(filter.getAgentCode())
                ? agent.getAgentCode()
                : filter.getAgentCode();

        // Trim -D suffix from agentCode if present
        agentCode = AgentCodeUtil.trimDSuffix(agentCode);

        // Set date range from filter or default to current month
        String dateFrom = filter.getStartDate().toString();
        String dateTo = filter.getEndDate().toString();

        // Set default values for other required fields
        String filterCode = null;
        String filterValue = null;

        if (StringUtils.isNotBlank(filter.getCustomerName())) {
            filterCode = "customerName";
            filterValue = filter.getCustomerName();
        }

        // Call the portal API
        Call<PortalBirthdayCustomerResponseDto> call = portalProvider.getBirthdayCustomerList(
                agentCode,
                dateFrom,
                dateTo,
                filterCode,
                filterValue
        );

        try {
            Response<PortalBirthdayCustomerResponseDto> response = call.execute();
            if (response.isSuccessful()) {
                PortalBirthdayCustomerResponseDto responseDto = response.body();
                if (responseDto != null && responseDto.getBirthdays() != null) {
                    // Convert the response data to BirthdayCustomerDto objects
                    return responseDto.getBirthdays().stream()
                            .map(this::convertToBirthdayCustomerDto)
                            .collect(Collectors.toList());
                }
            }
            // Return empty list if response is not successful or body is null
            return new ArrayList<>();
        } catch (IOException e) {
            log.error("Error occurred while retrieving birthday customer list", e);
            throw new InternalServerErrorException("Error occurred while retrieving birthday customer list");
        }
    }

    /**
     * Convert PortalBirthdayCustomerResponseDto.BirthdayCustomerDto to BirthdayCustomerDto
     */
    private BirthdayCustomerDto convertToBirthdayCustomerDto(PortalBirthdayCustomerResponseDto.BirthdayCustomerDto birthdayData) {
        BirthdayCustomerDto birthdayDto = new BirthdayCustomerDto();
        BeanUtils.copyProperties(birthdayData, birthdayDto);
        return birthdayDto;
    }
}
