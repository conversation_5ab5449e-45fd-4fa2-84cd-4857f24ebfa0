package id.co.panindaiichilife.superapp.agent.config.retrofit.portal;


import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.*;
import retrofit2.Call;
import retrofit2.http.*;

public interface PortalProvider {

    @POST("ads-agent/v2/auth/login")
    Call<PortalLoginResponseDto> login(@Body PortalLoginDto request);

    @POST("ads-agent/v1/agent/request-change-password")
    Call<PortalRequestChangePasswordResponseDto> requestChangePassword(@Body PortalRequestChangePasswordDto request);

    @POST("ads-agent/v1/agent/forgot-password")
    Call<PortalForgotPasswordResponseDto> forgotPassword(@Body PortalForgotPasswordDto request);

    @POST("ads-agent/v1/agent/change-password")
    Call<PortalChangePasswordResponseDto> changePassword(@Body PortalChangePasswordDto request);


    @GET("ads-agent/v1/common/mbranch")
    Call<PortalBranchResponseDto> getBranch(@Query("categoryCode") String categoryCode);

    @GET("ads-agent/v1/agent/trainings")
    Call<PortalTrainingResponseDto> getTrainingList(@Query("distributionCode") String distributionCode);

    @GET("ads-agent/v1/agent/training-pass")
    Call<PortalTrainingPassResponseDto> getTrainingPass(@Query("agentCode") String agentCode,
                                                        @Query("distributionCode") String distributionCode,
                                                        @Query("trainingCodeList") String trainingCodeList);

    @POST("plias-spaj/v1/spaj/list")
    Call<PortalSpajResponseDto> getSpajList(@Body PortalSpajDto request);

    @GET("ads-agent/v1/policy/claim/track")
    Call<PortalClaimResponseDto> getClaimList(
            @Query("agentCode") String agentCode,
            @Query("dateFrom") String dateFrom,
            @Query("dateTo") String dateTo,
            @Query("pageIndex") Integer pageIndex,
            @Query("pageSize") Integer pageSize,
            @Query("ClaimStatus") String claimStatus,
            @Query("withDownline") Integer withDownline
    );

    @POST("plias-policy/v1/policies/policies")
    Call<PortalPolicyResponseDto> getPolicyList(@Body PortalPolicyDto request);

    @GET("ads-agent/v1/customer/birthday-range")
    Call<PortalBirthdayCustomerResponseDto> getBirthdayCustomerList(
            @Query("agentCode") String agentCode,
            @Query("dateFrom") String dateFrom,
            @Query("dateTo") String dateTo,
            @Query("filterCode") String filterCode,
            @Query("filterValue") String filterValue
    );

    @GET("ads-agent/v1/customer/birthday-range")
    Call<PortalBirthdayCustomerResponseDto> getBirthdayCustomerListPageable(
            @Query("agentCode") String agentCode,
            @Query("dateFrom") String dateFrom,
            @Query("dateTo") String dateTo,
            @Query("pageIndex") Integer pageIndex,
            @Query("pageSize") Integer pageSize,
            @Query("filterCode") String filterCode,
            @Query("filterValue") String filterValue
    );

    @GET("ads-agent/v1/policy/payment/overdue")
    Call<PortalPolicyOverdueResponseDto> getPolicyOverdueList(
            @Query("agentCode") String agentCode,
            @Query("dateFrom") String dateFrom,
            @Query("dateTo") String dateTo,
            @Query("pageSize") Integer pageSize,
            @Query("orderBy") String orderBy,
            @Query("descending") Integer descending,
            @Query("PolicyHolderName") String policyHolderName,
            @Query("PolicyNumber") String policyNumber
    );

    @PUT("ads-agent/v1/agent/update/terminate")
    Call<PortalAgentTerminationResponseDto> terminateAgent(@Body PortalAgentTerminationRequestDto request);

    @PATCH("ads-agent/v1/agent/info")
    Call<PortalAgentInfoResponseDto> updateAgentInfo(@Query("agentCode") String agentCode,
                                                     @Body PortalEditProfileDto request);

    @GET("ads-agent/v1/agent/info")
    Call<PortalAgentInfoDetailResponseDto> getDetailedAgentInfo(@Query("agentCode") String agentCode);

    @POST("ads-agent/v1/agent/registration")
    Call<PortalRegistrationResponseDto> registerAgent(@Body PortalRegistrationDto request);

    @GET("ads-agent/v1/common/combo")
    Call<PortalBankResponseDto> getBankList(@Query("categoryCode") String categoryCode);

    @POST("plias-crm/v1/agent/info/AML/plias/doScreening")
    Call<PortalValidationAmlResponseDto> validateAml(@Body PortalValidationAmlDto request);

    @POST("advanced-ai/v1/ai/db/ids")
    Call<PortalValidationKtpResponseDto> validateKtp(@Body PortalValidationKtpDto request);

    @POST("advanced-ai/v1/ai/banks/accounts")
    Call<PortalValidationBankAccountResponseDto> validateBankAccount(@Body PortalValidationBankAccountDto request);

    @GET("ads-agent/v1/agent/info/internal-status")
    Call<PortalValidationBlacklistResponseDto> validateBlacklist(
            @Query("dob") String dateOfBirth,
            @Query("idNumber") String idNumber
    );

    @GET("ads-agent/v1/agent/info/agency-admin")
    Call<PortalValidationAdminAgentResponseDto> validateAdminAgent(
            @Query("idNo") String idNumber,
            @Query("status") String status
    );

    @GET("ads-agent/v1/agent/upline")
    Call<PortalValidationHirarkiResponseDto> getAgentUpline(
            @Query("recruiterCode") String recruiterCode,
            @Query("recruiteeLevel") String recruiteeLevel
    );

    @GET("ads-agent/v1/agent/reactivation/validation")
    Call<PortalAgentReactivationValidationResponseDto> validateAgentReactivation(
            @Query("agentCode") String agentCode,
            @Query("leaderCode") String leaderCode,
            @Query("branchCode") String branchCode,
            @Query("targetLvl") String targetLvl
    );

    @PUT("ads-agent/v1/agent/update/reactivate")
    Call<PortalAgentReactivationResponseDto> submitAgentReactivation(@Body PortalAgentReactivationRequestDto request);

    @GET("ads-agent/v1/agent/get-policy-servicing")
    Call<PortalGetPolicyServicingResponseDto> getPolicyServicingByAgentCode(@Query("agentCode") String agentCode);
}
