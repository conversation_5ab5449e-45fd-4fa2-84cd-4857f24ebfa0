package id.co.panindaiichilife.superapp.agent.service.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.PolicyOverdueDto;
import id.co.panindaiichilife.superapp.agent.api.filter.PolicyOverdueFilter;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.PortalProvider;
import id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto.PortalPolicyOverdueResponseDto;
import id.co.panindaiichilife.superapp.agent.core.http.InternalServerErrorException;
import id.co.panindaiichilife.superapp.agent.core.http.NotFoundException;
import id.co.panindaiichilife.superapp.agent.core.support.annotation.CacheableWithTTL;
import id.co.panindaiichilife.superapp.agent.core.util.AgentCodeUtil;
import id.co.panindaiichilife.superapp.agent.enums.PolicyOverdueType;
import id.co.panindaiichilife.superapp.agent.model.Agent;
import id.co.panindaiichilife.superapp.agent.model.User;
import id.co.panindaiichilife.superapp.agent.repository.AgentRepository;
import id.co.panindaiichilife.superapp.agent.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PolicyOverdueService {

    private final UserRepository userRepository;

    private final AgentRepository agentRepository;

    private final PortalProvider portalProvider;

    /**
     * Get policy overdue list for an agent
     *
     * @param username The username of the agent
     * @param filter   The filter parameters
     * @return List of policy overdue DTOs
     */
    @CacheableWithTTL(cacheName = "policyOverdueListCache",
            key = "(T(java.util.Objects).nonNull(#filter.agentCode) ? #filter.agentCode : #username) + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.size) ? #filter.size : '-') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.policyNumber) ? #filter.policyNumber : '-') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.policyHolderName) ? #filter.policyHolderName : '-') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.startDate) ? #filter.startDate : 'default') + ':' + " +
                    "(T(java.util.Objects).nonNull(#filter.endDate) ? #filter.endDate : 'default')",
            ttl = 1600, db = 7)
    public List<PolicyOverdueDto> getPolicyOverdueList(String username, PolicyOverdueFilter filter) {
        // Get the agent information from the username
        User user = userRepository.findByUsername(username).orElseThrow(NotFoundException::new);
        Agent agent = agentRepository.findTopByUser(user).orElseThrow(NotFoundException::new);

        // If agentCode is not provided in the filter, use the agent's code
        String agentCode = StringUtils.isBlank(filter.getAgentCode())
                ? agent.getAgentCode()
                : filter.getAgentCode();

        // Trim -D suffix from agentCode if present
        agentCode = AgentCodeUtil.trimDSuffix(agentCode);

        String dateFrom = filter.getStartDate().toString();
        String dateTo = filter.getEndDate().toString();

        String orderBy = "dueDate";
        int descending = 1;
        // Handle null type safely - default to PAST behavior (descending = 1)
        if (filter.getType() != null && PolicyOverdueType.UPCOMING.equals(filter.getType())) {
            descending = 0;
        }

        // Call the portal API
        Call<PortalPolicyOverdueResponseDto> call = portalProvider.getPolicyOverdueList(agentCode, dateFrom, dateTo, filter.getSize(), orderBy, descending, filter.getPolicyHolderName(), filter.getPolicyNumber());

        try {
            Response<PortalPolicyOverdueResponseDto> response = call.execute();
            if (response.isSuccessful()) {
                PortalPolicyOverdueResponseDto responseDto = response.body();
                if (responseDto != null && responseDto.getOutstandingDues() != null) {
                    // Convert the response data to PolicyOverdueDto objects
                    return responseDto.getOutstandingDues().stream()
                            .map(this::convertToPolicyOverdueDto)
                            .collect(Collectors.toList());
                }
            }
            // Return empty list if response is not successful or body is null
            return new ArrayList<>();
        } catch (IOException e) {
            log.error("Error occurred while retrieving policy overdue list", e);
            throw new InternalServerErrorException("Error occurred while retrieving policy overdue list");
        }
    }

    /**
     * Convert PortalPolicyOverdueResponseDto.OutstandingDueDto to PolicyOverdueDto
     */
    private PolicyOverdueDto convertToPolicyOverdueDto(PortalPolicyOverdueResponseDto.OutstandingDueDto overdueData) {
        PolicyOverdueDto policyOverdueDto = new PolicyOverdueDto();
        BeanUtils.copyProperties(overdueData, policyOverdueDto);
        return policyOverdueDto;
    }
}
