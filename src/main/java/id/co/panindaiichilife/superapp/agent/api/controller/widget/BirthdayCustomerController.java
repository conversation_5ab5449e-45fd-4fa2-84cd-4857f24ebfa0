package id.co.panindaiichilife.superapp.agent.api.controller.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.BirthdayCustomerDto;
import id.co.panindaiichilife.superapp.agent.api.filter.BirthdayCustomerFilter;
import id.co.panindaiichilife.superapp.agent.service.widget.BirthdayCustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.time.LocalDate;
import java.util.List;

@RestController("widgetBirthdayCustomerController")
@RequestMapping("/api/widget")
@Tag(name = "Widget", description = "API Widget")
@Slf4j
@RequiredArgsConstructor
public class BirthdayCustomerController {

    private final BirthdayCustomerService birthdayCustomerService;

    @Operation(summary = "Get widget birthday customer")
    @GetMapping(value = "birthday-customer")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.BirthdayCustomer', 'view')")
    public List<BirthdayCustomerDto> getBirthdayCustomer(Principal principal,
                                                         @ParameterObject @ModelAttribute("filter") BirthdayCustomerFilter filter) {

        LocalDate now = LocalDate.now();
        if (filter.getStartDate() == null) {
            filter.setStartDate(now.withDayOfMonth(1));
        }
        if (filter.getEndDate() == null) {
            filter.setEndDate(now.withDayOfMonth(now.lengthOfMonth()));
        }

        return birthdayCustomerService.getBirthdayCustomerList(principal.getName(), filter);
    }
}
