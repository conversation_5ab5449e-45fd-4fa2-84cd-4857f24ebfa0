package id.co.panindaiichilife.superapp.agent.api.controller.widget;

import id.co.panindaiichilife.superapp.agent.api.dto.widget.PolicyOverdueDto;
import id.co.panindaiichilife.superapp.agent.api.filter.PolicyOverdueFilter;
import id.co.panindaiichilife.superapp.agent.enums.PolicyOverdueType;
import id.co.panindaiichilife.superapp.agent.service.widget.PolicyOverdueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;
import java.time.LocalDate;
import java.util.List;

@RestController("widgetPolicyOverdueController")
@RequestMapping("/api/widget")
@Tag(name = "Widget", description = "API Widget")
@Slf4j
@RequiredArgsConstructor
public class PolicyOverdueController {

    private final PolicyOverdueService policyOverdueService;

    @Operation(summary = "Get widget Policy Overdue")
    @GetMapping(value = "policy-overdue")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN') or hasPermission('agent.Widget.PolicyOverdue', 'view')")
    public List<PolicyOverdueDto> getPolicyOverdue(Principal principal,
                                                   @ParameterObject @ModelAttribute("filter") PolicyOverdueFilter filter) {

        LocalDate now = LocalDate.now();

        // Handle null type by defaulting to PAST
        PolicyOverdueType type = filter.getType() != null ? filter.getType() : PolicyOverdueType.UPCOMING;

        if (PolicyOverdueType.UPCOMING.equals(type)) {
            filter.setStartDate(now.plusDays(1));
            filter.setEndDate(now.withDayOfMonth(now.lengthOfMonth()).plusMonths(3));
        } else {
            filter.setStartDate(now.withDayOfMonth(1).minusMonths(3));
            filter.setEndDate(now);
        }

        // Set the type back to the filter to ensure consistency
        filter.setType(type);


        return policyOverdueService.getPolicyOverdueList(principal.getName(), filter);
    }
}
