package id.co.panindaiichilife.superapp.agent.config.retrofit.portal.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class PortalBirthdayCustomerResponseDto {

    @SerializedName("statusCode")
    @JsonProperty("statusCode")
    private String statusCode;

    @SerializedName("message")
    @JsonProperty("message")
    private String message;

    @SerializedName("birthdays")
    @JsonProperty("birthdays")
    private List<BirthdayCustomerDto> birthdays;

    @Data
    public static class BirthdayCustomerDto {

        @SerializedName("agentCode")
        @JsonProperty("agentCode")
        private String agentCode;

        @SerializedName("customerName")
        @JsonProperty("customerName")
        private String customerName;

        @SerializedName("birthday")
        @JsonProperty("birthday")
        private String birthday;

        @SerializedName("phoneNumber")
        @JsonProperty("phoneNumber")
        private String phoneNumber;

        @SerializedName("emailAddress")
        @JsonProperty("emailAddress")
        private String emailAddress;

        @SerializedName("policyNumber")
        @JsonProperty("policyNumber")
        private String policyNumber;

        @SerializedName("relation")
        @JsonProperty("relation")
        private String relation;
    }
}
